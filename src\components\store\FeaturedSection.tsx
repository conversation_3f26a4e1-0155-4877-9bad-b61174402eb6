'use client';

import React, { useState } from 'react';
import { Crown, ArrowRight, Star, Sparkles, Diamond } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import FeaturedCard, { FeaturedCardItem } from './FeaturedCard';
import { featuredCardsMockData } from '@/data/featuredCardsMockData';
import { EnhancedStoreCard, StoreCardHeader, StoreButton } from './EnhancedStoreCard';

interface FeaturedSectionProps {
  lang: string;
}

const FeaturedSection: React.FC<FeaturedSectionProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const [featuredItems, setFeaturedItems] = useState<FeaturedCardItem[]>(featuredCardsMockData);
  const [purchasingItemId, setPurchasingItemId] = useState<string | null>(null);

  const handlePurchase = async (item: FeaturedCardItem) => {
    try {
      setPurchasingItemId(item.id);

      // Simulate purchase process
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Update the item as purchased
      setFeaturedItems(prev => prev.map(prevItem =>
        prevItem.id === item.id
          ? {
              ...prevItem,
              isPurchased: true,
              purchaseCount: (prevItem.purchaseCount || 0) + 1
            }
          : prevItem
      ));

      console.log('Purchase successful:', item.name);
    } catch (error) {
      console.error('Purchase failed:', error);
    } finally {
      setPurchasingItemId(null);
    }
  };





  return (
    <div className="space-y-6">

      {/* Featured Items Responsive Grid - Unified Layout */}
      <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
        {featuredItems.map((item) => (
          <FeaturedCard
            key={item.id}
            item={item}
            onPurchase={handlePurchase}
            isLoading={purchasingItemId === item.id}
            variant="responsive"
          />
        ))}
      </div>


      {/* Enhanced Premium Subscription Promotion with ISFJ warmth */}
      <EnhancedStoreCard
        gradient="from-indigo-500/15 via-purple-500/15 to-pink-500/15"
        glowEffect={true}
        vipGlow={true}
        neonReflection={true}
        className="mt-6"
      >
        <StoreCardHeader
          icon={Crown}
          title={t('store.featured.premiumPassOffer')}
          subtitle={t('store.featured.premiumPassDescription')}
          iconGradient="from-indigo-500 to-purple-600"
          actions={
            <div className="flex items-center gap-2 text-sm text-foreground/60">
              <Star className="w-4 h-4 text-yellow-400" fill="currentColor" />
              <span>{t('store.featured.popular')}</span>
            </div>
          }
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {/* Price highlight */}
          <div className="text-center p-4 bg-gradient-to-br from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-xl">
            <div className="text-3xl font-bold text-yellow-400 mb-1">$9.99</div>
            <div className="text-xs text-foreground/60">{t('store.featured.perMonth')}</div>
            <div className="text-xs text-green-400 mt-1">{t('store.featured.discount')}</div>
          </div>

          {/* Features highlight */}
          <div className="text-center p-4 bg-gradient-to-br from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-xl">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Sparkles className="w-4 h-4 text-purple-400" />
              <span className="text-lg font-bold text-purple-400">{t('store.featured.unlimited')}</span>
            </div>
            <div className="text-xs text-foreground/60">{t('store.featured.conversations')}</div>
            <div className="text-xs text-purple-400 mt-1">{t('store.featured.noLimits')}</div>
          </div>

          {/* Premium features */}
          <div className="text-center p-4 bg-gradient-to-br from-pink-500/20 to-rose-500/20 border border-pink-500/30 rounded-xl">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Diamond className="w-4 h-4 text-pink-400" fill="currentColor" />
              <span className="text-lg font-bold text-pink-400">{t('store.featured.premium')}</span>
            </div>
            <div className="text-xs text-foreground/60">{t('store.featured.features')}</div>
            <div className="text-xs text-pink-400 mt-1">{t('store.featured.exclusive')}</div>
          </div>
        </div>

        <div className="flex justify-center">
          <StoreButton
            variant="primary"
            size="lg"
            neonGlow={true}
            className="px-8 py-3"
          >
            <Crown className="w-5 h-5 mr-2" />
            {t('store.actions.subscribeNow')}
            <ArrowRight className="w-5 h-5 ml-2" />
          </StoreButton>
        </div>
      </EnhancedStoreCard>
    </div>
  );
};

export default FeaturedSection;
